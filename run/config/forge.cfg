# Configuration file

client {
    # Replace the vanilla bucket models with Forges own dynamic bucket model. Unifies bucket visuals if a mod uses the Forge bucket model.
    B:replaceVanillaBucketModel=false
}


general {
    # Set to true to disable Forge's version check mechanics. Forge queries a small json file on our server for version information. For more details see the ForgeVersion class in our github.
    B:disableVersionCheck=false

    # Controls the number threshold at which Packet51 is preferred over Packet52, default and minimum 64, maximum 1024
    I:clumpingThreshold=64

    # Set to true to enable the post initialization sorting of crafting recipes using <PERSON><PERSON>'s sorter. May cause desyncing on conflicting recipies. MUST RESTART MINECRAFT IF CHANGED FROM THE CONFIG GUI.
    B:sortRecipies=true

    # Set this to true to remove any Entity that throws an error in its update method instead of closing the server and reporting a crash log. BE WARNED THIS COULD SCREW UP EVERYTHING USE SPARINGLY WE ARE NOT RESPONSIBLE FOR DAMAGES.
    B:removeErroringEntities=false

    # Set this to true to remove any TileEntity that throws an error in its update method instead of closing the server and reporting a crash log. BE WARNED THIS COULD SCREW UP EVERYTHING USE SPARINGLY WE ARE NOT RESPONSIBLE FOR DAMAGES.
    B:removeErroringTileEntities=false

    # Set this to true to check the entire entity's collision bounding box for ladders instead of just the block they are in. Causes noticable differences in mechanics so default is vanilla behavior. Default: false
    B:fullBoundingBoxLadders=false

    # Control the range of sky blending for colored skies in biomes.
    I:biomeSkyBlendRange <
        2
        4
        6
        8
        10
        12
        14
        16
        18
        20
        22
        24
        26
        28
        30
        32
        34
     >

    # Base zombie summoning spawn chance. Allows changing the bonus zombie summoning mechanic.
    D:zombieBaseSummonChance=0.1

    # Chance that a zombie (or subclass) is a baby. Allows changing the zombie spawning mechanic.
    D:zombieBabyChance=0.05

    # The spawn fuzz when a player respawns in the world, this is controlable by WorldType, this config option is for the default overworld.
    I:defaultSpawnFuzz=20

    # If the overworld has ANY spawn fuzz at all. If not, the spawn will always be the exact same location.
    B:spawnHasFuzz=true

    # Enable the forge block rendering pipeline - fixes the lighting of custom models.
    B:forgeLightPipelineEnabled=true

    # Set this to just disable the texture stitcher from writing the '{name}_{mipmap}.png files to disc. Just a small performance tweak. Default: true
    B:disableStitchedFileSaving=true
    B:enableGlobalConfig=false
}


version_checking {
    # Enable the entire mod update check system. This only applies to mods using the Forge system.
    B:Global=true
}


