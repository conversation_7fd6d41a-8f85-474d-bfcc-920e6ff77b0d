[11:53:40] [main/INFO]: Extra: []
[11:53:40] [main/INFO]: Running with arguments: [--userProperties, {}, --assetsDir, C:/Users/<USER>/Downloads/DarkClient-master/.gradle/caches/minecraft/assets, --assetIndex, 1.8, --accessToken{REDACTED}, --version, 1.8.9, --tweakClass, net.minecraftforge.fml.common.launcher.FMLTweaker, --tweakClass, net.minecraftforge.gradle.tweakers.CoremodTweaker]
[11:53:40] [main/INFO]: Loading tweak class name net.minecraftforge.fml.common.launcher.FMLTweaker
[11:53:40] [main/INFO]: Using primary tweak class name net.minecraftforge.fml.common.launcher.FMLTweaker
[11:53:40] [main/INFO]: Loading tweak class name net.minecraftforge.gradle.tweakers.CoremodTweaker
[11:53:40] [main/INFO]: Calling tweak class net.minecraftforge.fml.common.launcher.FMLTweaker
[11:53:40] [main/INFO]: Forge Mod Loader version 11.15.1.1722 for Minecraft 1.8.9 loading
[11:53:40] [main/INFO]: Java is Java HotSpot(TM) 64-Bit Server VM, version 1.8.0_202, running on Windows 10:amd64:10.0, installed at C:\Program Files\Java\jdk1.8.0_202\jre
[11:53:40] [main/INFO]: Managed to load a deobfuscated Minecraft name- we are in a deobfuscated environment. Skipping runtime deobfuscation
[11:53:40] [main/INFO]: Calling tweak class net.minecraftforge.gradle.tweakers.CoremodTweaker
[11:53:40] [main/INFO]: Injecting location in coremod net.minecraftforge.fml.relauncher.FMLCorePlugin
[11:53:40] [main/INFO]: Injecting location in coremod net.minecraftforge.classloading.FMLForgePlugin
[11:53:40] [main/INFO]: Loading tweak class name net.minecraftforge.fml.common.launcher.FMLInjectionAndSortingTweaker
[11:53:40] [main/INFO]: Loading tweak class name net.minecraftforge.fml.common.launcher.FMLDeobfTweaker
[11:53:40] [main/INFO]: Loading tweak class name net.minecraftforge.gradle.tweakers.AccessTransformerTweaker
[11:53:40] [main/INFO]: Calling tweak class net.minecraftforge.fml.common.launcher.FMLInjectionAndSortingTweaker
[11:53:40] [main/INFO]: Calling tweak class net.minecraftforge.fml.common.launcher.FMLInjectionAndSortingTweaker
[11:53:40] [main/INFO]: Calling tweak class net.minecraftforge.fml.relauncher.CoreModManager$FMLPluginWrapper
[11:53:40] [main/ERROR]: The binary patch set is missing. Either you are in a development environment, or things are not going to work!
[11:53:41] [main/ERROR]: FML appears to be missing any signature data. This is not a good thing
[11:53:41] [main/INFO]: Calling tweak class net.minecraftforge.fml.relauncher.CoreModManager$FMLPluginWrapper
[11:53:41] [main/INFO]: Calling tweak class net.minecraftforge.fml.common.launcher.FMLDeobfTweaker
[11:53:41] [main/INFO]: Calling tweak class net.minecraftforge.gradle.tweakers.AccessTransformerTweaker
[11:53:41] [main/INFO]: Loading tweak class name net.minecraftforge.fml.common.launcher.TerminalTweaker
[11:53:41] [main/INFO]: Calling tweak class net.minecraftforge.fml.common.launcher.TerminalTweaker
[11:53:41] [main/INFO]: Launching wrapped minecraft {net.minecraft.client.main.Main}
[11:53:42] [Client thread/INFO]: Setting user: Player886
[11:53:45] [Client thread/INFO]: LWJGL Version: 2.9.4
[11:53:47] [Client thread/WARN]: =============================================================
[11:53:47] [Client thread/WARN]: MOD HAS DIRECT REFERENCE System.exit() THIS IS NOT ALLOWED REROUTING TO FML!
[11:53:47] [Client thread/WARN]: Offendor: com/sun/jna/Native.main([Ljava/lang/String;)V
[11:53:47] [Client thread/WARN]: Use FMLCommonHandler.exitJava instead
[11:53:47] [Client thread/WARN]: =============================================================
[11:53:48] [Client thread/INFO]: [net.minecraftforge.fml.client.SplashProgress:start:246]: ---- Minecraft Crash Report ----
// Surprise! Haha. Well, this is awkward.

Time: 2/08/25 11:53
Description: Loading screen debug info

This is just a prompt for computer specs to be printed. THIS IS NOT A ERROR


A detailed walkthrough of the error, its code path and all known details is as follows:
---------------------------------------------------------------------------------------

-- System Details --
Details:
	Minecraft Version: 1.8.9
	Operating System: Windows 10 (amd64) version 10.0
	Java Version: 1.8.0_202, Oracle Corporation
	Java VM Version: Java HotSpot(TM) 64-Bit Server VM (mixed mode), Oracle Corporation
	Memory: 217166160 bytes (207 MB) / 599785472 bytes (572 MB) up to 1834483712 bytes (1749 MB)
	JVM Flags: 0 total; 
	IntCache: cache: 0, tcache: 0, allocated: 0, tallocated: 0
	FML: 
	Loaded coremods (and transformers): 
	GL info: ' Vendor: 'Intel' Version: '4.6.0 - Build 32.0.101.6913' Renderer: 'Intel(R) UHD Graphics'
[11:53:48] [Client thread/INFO]: MinecraftForge v11.15.1.1722 Initialized
[11:53:48] [Client thread/INFO]: Replaced 204 ore recipies
[11:53:49] [Client thread/INFO]: Found 0 mods from the command line. Injecting into mod discoverer
[11:53:49] [Client thread/INFO]: Searching C:\Users\<USER>\Downloads\DarkClient-master\run\mods for mods
[11:53:50] [Client thread/INFO]: Forge Mod Loader has identified 4 mods to load
[11:53:51] [Client thread/INFO]: Attempting connection with missing mods [mcp, FML, Forge, darkclient] at CLIENT
[11:53:51] [Client thread/INFO]: Attempting connection with missing mods [mcp, FML, Forge, darkclient] at SERVER
[11:53:51] [Client thread/INFO]: Reloading ResourceManager: Default, FMLFileResourcePack:Forge Mod Loader, FMLFileResourcePack:Minecraft Forge, FMLFileResourcePack:Pelusa Dark Client
[11:53:51] [Client thread/INFO]: Processing ObjectHolder annotations
[11:53:51] [Client thread/INFO]: Found 384 ObjectHolder annotations
[11:53:51] [Client thread/INFO]: Identifying ItemStackHolder annotations
[11:53:51] [Client thread/INFO]: Found 0 ItemStackHolder annotations
[11:53:51] [Client thread/INFO]: Configured a dormant chunk cache size of 0
[11:53:51] [Forge Version Check/INFO]: [Forge] Starting version check at http://files.minecraftforge.net/maven/net/minecraftforge/forge/promotions_slim.json
[11:53:51] [Client thread/INFO]: Applying holder lookups
[11:53:51] [Client thread/INFO]: Holder lookups applied
[11:53:51] [Client thread/INFO]: Injecting itemstacks
[11:53:51] [Client thread/INFO]: Itemstack injection complete
[11:53:52] [Sound Library Loader/INFO]: Starting up SoundSystem...
[11:53:52] [Thread-8/INFO]: Initializing LWJGL OpenAL
[11:53:52] [Thread-8/INFO]: (The LWJGL binding of OpenAL.  For more information, see http://www.lwjgl.org)
[11:53:53] [Thread-8/INFO]: OpenAL initialized.
[11:53:53] [Sound Library Loader/INFO]: Sound engine started
[11:53:59] [Client thread/INFO]: Max texture size: 16384
[11:53:59] [Client thread/INFO]: Created: 16x16 textures-atlas
[11:54:00] [Client thread/INFO]: Injecting itemstacks
[11:54:00] [Client thread/INFO]: Itemstack injection complete
[11:54:00] [Client thread/INFO]: Forge Mod Loader has successfully loaded 4 mods
[11:54:00] [Client thread/INFO]: Reloading ResourceManager: Default, FMLFileResourcePack:Forge Mod Loader, FMLFileResourcePack:Minecraft Forge, FMLFileResourcePack:Pelusa Dark Client
[11:54:00] [Client thread/INFO]: SoundSystem shutting down...
[11:54:01] [Client thread/WARN]: Author: Paul Lamb, www.paulscode.com
[11:54:01] [Sound Library Loader/INFO]: Starting up SoundSystem...
[11:54:01] [Thread-10/INFO]: Initializing LWJGL OpenAL
[11:54:01] [Thread-10/INFO]: (The LWJGL binding of OpenAL.  For more information, see http://www.lwjgl.org)
[11:54:01] [Thread-10/INFO]: OpenAL initialized.
[11:54:01] [Sound Library Loader/INFO]: Sound engine started
[11:54:07] [Client thread/INFO]: Max texture size: 16384
[11:54:08] [Client thread/INFO]: Created: 512x512 textures-atlas
