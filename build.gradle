buildscript {
    repositories {
        mavenCentral()
        maven { url = 'https://maven.minecraftforge.net' }
        maven { url = 'https://repo.spongepowered.org/maven' }
    }
    dependencies {
        classpath 'net.minecraftforge.gradle:ForgeGradle:2.1-SNAPSHOT'
    }
}
apply plugin: 'net.minecraftforge.gradle.forge'

sourceCompatibility = targetCompatibility = '1.8'

compileJava {
    sourceCompatibility = targetCompatibility = '1.8'
    options.encoding = 'UTF-8'
}

version = "2.0"
group= "com.pelusa.dark"
archivesBaseName = "ghostdark"/*TODO: epa te estoy viendo pillo, Dame los Creditos Si lo vas a usar O-o*/

minecraft {
    version = "1.8.9-11.15.1.1722"
    runDir = "run"
    mappings = "stable_20"
}

dependencies {
/*Recuerda darme Creditos Si Vas a usar Mi ghost*/
}

// Suprimir warnings y deprecation notices
gradle.projectsEvaluated {
    tasks.withType(JavaCompile) {
        options.compilerArgs << "-Xlint:-deprecation"
        options.compilerArgs << "-Xlint:-unchecked"
        options.compilerArgs << "-nowarn"
    }
}

// Suprimir warnings de Gradle deprecated
System.setProperty("org.gradle.warning.mode", "none")

// Suprimir warnings específicos de ForgeGradle
ext.suppressWarnings = true

// Configurar logging para suprimir warnings
gradle.beforeProject { project ->
    project.logging.captureStandardOutput LogLevel.INFO
}

processResources
{
    inputs.property "version", project.version
    inputs.property "mcversion", project.minecraft.version
    from(sourceSets.main.resources.srcDirs) {
        include 'mcmod.info'
        expand 'version':project.version, 'mcversion':project.minecraft.version
    }
    from(sourceSets.main.resources.srcDirs) {
        exclude 'mcmod.info'
    }
}
